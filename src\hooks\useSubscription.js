import React from 'react';
import { GlobalContext } from '../globalContext';
import { 
  getUserSubscriptionProductId, 
  getSubscriptionTypeFromProductId,
  hasAdvancedFeatures,
  hasSubscriptionAccess,
  getProjectLimit,
  getSubscriptionDisplayName
} from '../utils/subscriptionUtils';

/**
 * Custom hook for managing subscription data throughout the application
 * Provides a consistent interface for accessing subscription information
 */
export const useSubscription = () => {
  const { state: globalState } = React.useContext(GlobalContext);
  const [userDetails, setUserDetails] = React.useState(null);
  const [subscriptionProductId, setSubscriptionProductId] = React.useState(null);

  // Get user details from localStorage and API if needed
  React.useEffect(() => {
    const getUserDetails = async () => {
      try {
        const userId = localStorage.getItem("user");
        if (userId) {
          // Try to get from global state first
          if (globalState.subscription?.currentSubscription) {
            const productId = globalState.subscription.currentSubscription.planId;
            setSubscriptionProductId(productId);
          } else {
            // Fallback to localStorage for backward compatibility
            const legacySubscription = localStorage.getItem("UserSubscription");
            if (legacySubscription) {
              // Convert legacy subscription to product ID
              const productId = getUserSubscriptionProductId({ subscription: parseInt(legacySubscription) });
              setSubscriptionProductId(productId);
            }
          }
        }
      } catch (error) {
        console.error('Error getting user subscription details:', error);
      }
    };

    getUserDetails();
  }, [globalState.subscription]);

  // Computed values based on subscription
  const subscriptionType = React.useMemo(() => {
    return getSubscriptionTypeFromProductId(subscriptionProductId);
  }, [subscriptionProductId]);

  const hasAdvanced = React.useMemo(() => {
    return hasAdvancedFeatures(subscriptionProductId);
  }, [subscriptionProductId]);

  const projectLimit = React.useMemo(() => {
    return getProjectLimit(subscriptionProductId);
  }, [subscriptionProductId]);

  const displayName = React.useMemo(() => {
    return getSubscriptionDisplayName(subscriptionProductId);
  }, [subscriptionProductId]);

  // Feature access helpers
  const hasStudioAccess = React.useMemo(() => {
    return hasSubscriptionAccess(subscriptionProductId, 'studio');
  }, [subscriptionProductId]);

  const hasPortalAccess = React.useMemo(() => {
    return hasSubscriptionAccess(subscriptionProductId, 'portal');
  }, [subscriptionProductId]);

  const hasCompleteAccess = React.useMemo(() => {
    return hasSubscriptionAccess(subscriptionProductId, 'complete');
  }, [subscriptionProductId]);

  return {
    // Core subscription data
    subscriptionProductId,
    subscriptionType, // For backward compatibility (1, 2, 3)
    displayName,
    
    // Feature access
    hasAdvanced, // Replaces parseInt(SubscriptionType) > 1
    hasStudioAccess,
    hasPortalAccess, 
    hasCompleteAccess,
    
    // Project limits
    projectLimit,
    
    // Global subscription state
    globalSubscription: globalState.subscription,
    
    // Utility functions
    checkFeatureAccess: (feature) => hasSubscriptionAccess(subscriptionProductId, feature),
  };
};
