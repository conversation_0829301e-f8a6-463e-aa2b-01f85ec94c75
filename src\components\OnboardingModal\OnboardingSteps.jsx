import React, { useState, useEffect } from "react";
import { GlobalContext, showToast } from "../../globalContext";
import MkdSDK from "../../utils/MkdSDK";
import { ClipLoader } from "react-spinners";

const OnboardingSteps = ({
  currentStep,
  stepData,
  userDetails,
  onStepComplete,
  onComplete,
  steps,
}) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [formData, setFormData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [termsAgreed, setTermsAgreed] = useState(false);
  const [privacyAgreed, setPrivacyAgreed] = useState(false);

  // Initialize form data from user details and step data
  useEffect(() => {
    const initialData = {
      // Business Profile
      companyName:
        userDetails?.company_name || stepData?.step_2_data?.companyName || "",
      officeEmail:
        userDetails?.office_email || stepData?.step_2_data?.officeEmail || "",
      companyAddress:
        userDetails?.company_address ||
        stepData?.step_2_data?.companyAddress ||
        "",
      phone: userDetails?.phone || stepData?.step_2_data?.phone || "",

      // Contact Details
      businessAddress: stepData?.step_8_data?.businessAddress || "",
      contactInfo: stepData?.step_8_data?.contactInfo || "",
    };
    setFormData(initialData);
  }, [userDetails, stepData]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleStepSubmit = async () => {
    try {
      setIsSubmitting(true);

      // Validate current step
      if (currentStep === 1) {
        if (!termsAgreed || !privacyAgreed) {
          showToast(
            globalDispatch,
            "Please agree to both Terms of Service and Privacy Policy",
            4000,
            "error"
          );
          return;
        }
      } else if (currentStep === 2) {
        if (!formData.companyName) {
          showToast(globalDispatch, "Company name is required", 4000, "error");
          return;
        }
      }

      // Save step data
      const stepDataToSave = {
        ...formData,
        termsAgreed:
          currentStep === 1 ? termsAgreed : stepData?.step_1_data?.termsAgreed,
        privacyAgreed:
          currentStep === 1
            ? privacyAgreed
            : stepData?.step_1_data?.privacyAgreed,
      };

      // Update user details if business profile step
      if (currentStep === 2) {
        const sdk = new MkdSDK();
        const updateResult = await sdk.callRawAPI(
          "/v3/api/custom/equality_record/user/update",
          {
            company_name: formData.companyName,
            office_email: formData.officeEmail,
            company_address: formData.companyAddress,
            phone: formData.phone,
          },
          "PUT"
        );

        if (updateResult.error) {
          throw new Error(
            updateResult.message || "Failed to update business profile"
          );
        }
      }

      onStepComplete(currentStep, stepDataToSave);
    } catch (error) {
      console.error("Error submitting step:", error);
      showToast(
        globalDispatch,
        error.message || "Failed to save step data",
        4000,
        "error"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Legal Agreement
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Please review and agree to our terms and policies to continue.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="terms"
                  checked={termsAgreed}
                  onChange={(e) => setTermsAgreed(e.target.checked)}
                  className="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  I agree to the{" "}
                  <a href="#" className="text-primary hover:underline">
                    Terms of Service
                  </a>
                </label>
              </div>

              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="privacy"
                  checked={privacyAgreed}
                  onChange={(e) => setPrivacyAgreed(e.target.checked)}
                  className="mt-1 h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <label
                  htmlFor="privacy"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  I agree to the{" "}
                  <a href="#" className="text-primary hover:underline">
                    Privacy Policy
                  </a>
                </label>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Business Profile
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Enter your company information to set up your business profile.
              </p>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Company Name *
                </label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange("companyName", e.target.value)
                  }
                  className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input"
                  placeholder="Enter your company name"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Office Email
                </label>
                <input
                  type="email"
                  value={formData.officeEmail}
                  onChange={(e) =>
                    handleInputChange("officeEmail", e.target.value)
                  }
                  className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input"
                  placeholder="Enter office email"
                />
              </div>

              <div className="md:col-span-2">
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Company Address
                </label>
                <textarea
                  value={formData.companyAddress}
                  onChange={(e) =>
                    handleInputChange("companyAddress", e.target.value)
                  }
                  rows={3}
                  className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input"
                  placeholder="Enter your company address"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-black dark:text-white">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className="w-full rounded-lg border border-stroke bg-transparent px-4 py-3 outline-none focus:border-primary dark:border-form-strokedark dark:bg-form-input"
                  placeholder="Enter phone number"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Client Service Agreement
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Set up your service agreement template for client projects.
              </p>
            </div>
            <div className="py-12 text-center">
              <p className="text-gray-500">
                Service agreement setup coming soon...
              </p>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Edit Policy Upload
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Upload your edit policy document for client reference.
              </p>
            </div>
            <div className="py-12 text-center">
              <p className="text-gray-500">Edit policy upload coming soon...</p>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Payment & Billing Settings
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Set up your Stripe account to receive payments from clients.
              </p>
            </div>

            {userDetails?.has_stripe ? (
              <div className="py-8 text-center">
                <div className="mb-4 flex justify-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                    <svg
                      className="h-8 w-8 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
                <h4 className="mb-2 text-lg font-semibold text-black dark:text-white">
                  Stripe Account Connected
                </h4>
                <p className="text-gray-600 dark:text-gray-300">
                  Your Stripe account is set up and ready to receive payments.
                </p>
              </div>
            ) : (
              <div className="py-8 text-center">
                <div className="mb-4 flex justify-center">
                  <div className="flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                    <svg
                      className="h-8 w-8 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                  </div>
                </div>
                <h4 className="mb-2 text-lg font-semibold text-black dark:text-white">
                  Set Up Stripe Payments
                </h4>
                <p className="mb-6 text-gray-600 dark:text-gray-300">
                  Connect your Stripe account to start receiving payments from
                  clients.
                </p>

                <div className="space-y-4">
                  <button
                    onClick={() => {
                      if (userDetails?.account_link?.url) {
                        window.open(userDetails.account_link.url, "_blank");
                      }
                    }}
                    className="w-full rounded-lg bg-primary px-6 py-3 text-white hover:bg-opacity-90"
                  >
                    Set Up Stripe Account
                  </button>

                  <p className="text-sm text-gray-500">
                    You can skip this step and set it up later in your settings.
                  </p>
                </div>
              </div>
            )}
          </div>
        );

      case 6:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Project Management Settings
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Set up default project management settings and workflows.
              </p>
            </div>
            <div className="py-12 text-center">
              <p className="text-gray-500">
                Project management settings coming soon...
              </p>
            </div>
          </div>
        );

      case 7:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Project Timelines & Defaults
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Configure default timelines and project settings.
              </p>
            </div>
            <div className="py-12 text-center">
              <p className="text-gray-500">
                Timeline configuration coming soon...
              </p>
            </div>
          </div>
        );

      case 8:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="mb-4 text-xl font-bold text-black dark:text-white">
                Full Contact Details
              </h3>
              <p className="mb-6 text-gray-600 dark:text-gray-300">
                Complete your contact information for client communications.
              </p>
            </div>
            <div className="py-12 text-center">
              <p className="text-gray-500">
                Contact details setup coming soon...
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const isLastStep = currentStep === steps.length;
  const canProceed = () => {
    if (currentStep === 1) {
      return termsAgreed && privacyAgreed;
    } else if (currentStep === 2) {
      return formData.companyName?.trim();
    }
    return true; // For other steps, allow proceeding
  };

  return (
    <div className="flex h-full flex-col">
      <div className="flex-1 overflow-y-auto">{renderStepContent()}</div>

      {/* Footer Actions */}
      <div className="border-t border-stroke pt-6 dark:border-strokedark">
        <div className="flex justify-between">
          <button
            onClick={() => {
              if (currentStep > 1) {
                // Go to previous step logic could be added here
              }
            }}
            disabled={currentStep === 1}
            className="rounded-lg border border-stroke px-6 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 dark:border-strokedark dark:text-gray-300 dark:hover:bg-gray-800"
          >
            Previous
          </button>

          <button
            onClick={isLastStep ? onComplete : handleStepSubmit}
            disabled={!canProceed() || isSubmitting}
            className="flex items-center gap-2 rounded-lg bg-primary px-6 py-2 text-sm font-medium text-white hover:bg-opacity-90 disabled:opacity-50"
          >
            {isSubmitting && <ClipLoader color="#fff" size={16} />}
            {isLastStep ? "Complete Setup" : "Continue"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default OnboardingSteps;
