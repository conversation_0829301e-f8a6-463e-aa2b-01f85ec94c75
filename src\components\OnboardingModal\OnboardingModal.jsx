import React, { useState, useEffect } from "react";
import { GlobalContext, showToast } from "../../globalContext";
import { AuthContext } from "../../authContext";
import MkdSDK from "../../utils/MkdSDK";
import { <PERSON>lipLoader } from "react-spinners";
import OnboardingSteps from "./OnboardingSteps";
import { getUserDetailsByIdAPI } from "Src/services/userService";

const OnboardingModal = ({ userDetails, onComplete, onMinimize }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [canClose, setCanClose] = useState(false);
  const [stepData, setStepData] = useState({});
  const [needsSubscription, setNeedsSubscription] = useState(false);

  // Initialize step data from user details
  useEffect(() => {
    if (userDetails) {
      // Check if user needs subscription
      const isSubMember =
        userDetails.main_user_details && !userDetails.main_user_details.is_self;
      const hasPlan = isSubMember
        ? userDetails.main_user_details.plan_id
        : userDetails.plan_id;

      // Note: Subscription check is now handled in login flow
      // If user reaches here, they should have subscription access

      if (userDetails.steps) {
        try {
          const parsedSteps = JSON.parse(userDetails.steps);
          setStepData(parsedSteps);

          // Check if business profile is complete (step 2)
          if (parsedSteps.business_profile_complete) {
            setCanClose(true);
          }
        } catch (e) {
          console.error("Error parsing step data:", e);
        }
      }
    }
  }, [userDetails]);

  const getSteps = () => {
    const baseSteps = [
      {
        id: needsSubscription ? 2 : 1,
        title: "Legal Agreement",
        description: "Terms of Service & Privacy Policy",
        icon: "📋",
        required: true,
      },
      {
        id: needsSubscription ? 3 : 2,
        title: "Business Profile",
        description: "Company Information",
        icon: "🏢",
        required: true,
      },
      {
        id: needsSubscription ? 4 : 3,
        title: "Client Service Agreement",
        description: "Service Agreement Setup",
        icon: "📄",
        required: false,
      },
      {
        id: needsSubscription ? 5 : 4,
        title: "Edit Policy Upload",
        description: "Upload Edit Policy Document",
        icon: "📤",
        required: false,
      },
      {
        id: needsSubscription ? 6 : 5,
        title: "Payment & Billing Settings",
        description: "Stripe Setup & Billing",
        icon: "💳",
        required: false,
      },
      {
        id: needsSubscription ? 7 : 6,
        title: "Project Management Settings",
        description: "Default Project Settings",
        icon: "⚙️",
        required: false,
      },
      {
        id: needsSubscription ? 8 : 7,
        title: "Project Timelines & Defaults",
        description: "Timeline Configuration",
        icon: "📅",
        required: false,
      },
      {
        id: needsSubscription ? 9 : 8,
        title: "Full Contact Details",
        description: "Complete Contact Information",
        icon: "📞",
        required: false,
      },
    ];

    if (needsSubscription) {
      return [
        {
          id: 1,
          title: "Subscription Plan",
          description: "Choose Your Plan",
          icon: "💎",
          required: true,
        },
        ...baseSteps,
      ];
    }

    return baseSteps;
  };

  const steps = getSteps();

  const handleStepComplete = (stepId, data) => {
    const updatedStepData = {
      ...stepData,
      [`step_${stepId}_complete`]: true,
      [`step_${stepId}_data`]: data,
    };

    // Special handling for business profile completion
    if (stepId === 2 && data.companyName) {
      updatedStepData.business_profile_complete = true;
      setCanClose(true);
    }

    setStepData(updatedStepData);

    // Move to next step if not the last one
    if (stepId < steps.length) {
      setCurrentStep(stepId + 1);
    } else {
      // All steps complete
      handleComplete();
    }
  };

  const handleComplete = async () => {
    try {
      setIsLoading(true);
      const sdk = new MkdSDK();

      const finalStepData = {
        ...stepData,
        onboarding_complete: true,
        completed_at: new Date().toISOString(),
      };

      // Update user with completed onboarding
      const updateResult = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/update",
        {
          steps: JSON.stringify(finalStepData),
        },
        "PUT"
      );

      if (!updateResult.error) {
        showToast(
          globalDispatch,
          "Onboarding completed successfully!",
          4000,
          "success"
        );
        onComplete();
      } else {
        throw new Error(
          updateResult.message || "Failed to complete onboarding"
        );
      }
    } catch (error) {
      console.error("Error completing onboarding:", error);
      showToast(
        globalDispatch,
        "Failed to complete onboarding. Please try again.",
        4000,
        "error"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleMinimize = () => {
    if (canClose) {
      onMinimize();
    } else {
      showToast(
        globalDispatch,
        "Please complete your business profile before minimizing.",
        4000,
        "warning"
      );
    }
  };

  const getStepStatus = (stepId) => {
    if (stepData[`step_${stepId}_complete`]) return "complete";
    if (stepId === currentStep) return "current";
    if (stepId < currentStep) return "incomplete";
    return "upcoming";
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="mx-4 w-full max-w-6xl rounded-lg bg-white shadow-xl dark:bg-boxdark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-stroke p-6 dark:border-strokedark">
          <div>
            <h2 className="text-2xl font-bold text-black dark:text-white">
              Complete Your Business Setup
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Small steps for big impact. Finish strong!
            </p>
          </div>
          <div className="flex items-center gap-4">
            {canClose && (
              <button
                onClick={handleMinimize}
                className="rounded-lg border border-stroke px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-strokedark dark:text-gray-300 dark:hover:bg-gray-800"
              >
                Minimize
              </button>
            )}
            <div className="text-sm text-gray-500">
              Step {currentStep} of {steps.length}
            </div>
          </div>
        </div>

        <div className="flex h-[600px]">
          {/* Left Sidebar - Steps */}
          <div className="w-80 border-r border-stroke bg-gray-50 p-6 dark:border-strokedark dark:bg-boxdark-2">
            <h3 className="mb-4 text-lg font-semibold text-black dark:text-white">
              Key Business Settings
            </h3>
            <div className="space-y-3">
              {steps.map((step) => {
                const status = getStepStatus(step.id);
                return (
                  <div
                    key={step.id}
                    className={`flex cursor-pointer items-center rounded-lg p-3 transition-colors ${
                      status === "current"
                        ? "bg-primary text-white"
                        : status === "complete"
                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                        : "bg-white text-gray-700 hover:bg-gray-100 dark:bg-boxdark dark:text-gray-300 dark:hover:bg-gray-800"
                    }`}
                    onClick={() => {
                      if (status === "complete" || step.id <= currentStep) {
                        setCurrentStep(step.id);
                      }
                    }}
                  >
                    <div className="mr-3 text-lg">{step.icon}</div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{step.title}</h4>
                        {status === "complete" && (
                          <svg
                            className="h-4 w-4 text-green-600"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        )}
                      </div>
                      <p className="text-xs opacity-75">{step.description}</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Content Area */}
          <div className="flex-1 p-6">
            {isLoading ? (
              <div className="flex h-full items-center justify-center">
                <ClipLoader color="#3B82F6" size={50} />
              </div>
            ) : (
              <OnboardingSteps
                currentStep={currentStep}
                stepData={stepData}
                userDetails={userDetails}
                onStepComplete={handleStepComplete}
                onComplete={handleComplete}
                steps={steps}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingModal;
