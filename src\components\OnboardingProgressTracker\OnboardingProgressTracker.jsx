import React, { useState, useEffect } from "react";

const OnboardingProgressTracker = ({ stepData, onReopen }) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);

  const steps = [
    { id: 1, title: "Legal Agreement", icon: "📋" },
    { id: 2, title: "Business Profile", icon: "🏢" },
    { id: 3, title: "Service Agreement", icon: "📄" },
    { id: 4, title: "Edit Policy", icon: "📤" },
    { id: 5, title: "Payment Settings", icon: "💳" },
    { id: 6, title: "Project Settings", icon: "⚙️" },
    { id: 7, title: "Timelines", icon: "📅" },
    { id: 8, title: "Contact Details", icon: "📞" },
  ];

  const getCompletedSteps = () => {
    return steps.filter(step => stepData[`step_${step.id}_complete`]).length;
  };

  const getProgressPercentage = () => {
    return Math.round((getCompletedSteps() / steps.length) * 100);
  };

  const handleToggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <div className="rounded-lg bg-white shadow-lg dark:bg-boxdark border border-stroke dark:border-strokedark">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-stroke p-3 dark:border-strokedark">
          <div className="flex items-center gap-2">
            <div className="text-sm font-medium text-black dark:text-white">
              Business Setup
            </div>
            <div className="rounded-full bg-primary px-2 py-0.5 text-xs text-white">
              {getCompletedSteps()}/{steps.length}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <button
              onClick={handleToggleMinimize}
              className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
            >
              <svg
                className={`h-4 w-4 transition-transform ${
                  isMinimized ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            <button
              onClick={handleClose}
              className="rounded p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-800 dark:hover:text-gray-300"
            >
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        {!isMinimized && (
          <div className="p-3">
            {/* Progress Bar */}
            <div className="mb-3">
              <div className="mb-1 flex justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-300">Progress</span>
                <span className="font-medium text-black dark:text-white">
                  {getProgressPercentage()}%
                </span>
              </div>
              <div className="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className="h-2 rounded-full bg-primary transition-all duration-300"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
            </div>

            {/* Steps List */}
            <div className="space-y-2">
              {steps.map((step) => {
                const isComplete = stepData[`step_${step.id}_complete`];
                return (
                  <div
                    key={step.id}
                    className={`flex items-center gap-2 rounded p-2 text-xs ${
                      isComplete
                        ? "bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                        : "bg-gray-50 text-gray-600 dark:bg-gray-800 dark:text-gray-400"
                    }`}
                  >
                    <span className="text-sm">{step.icon}</span>
                    <span className="flex-1 truncate">{step.title}</span>
                    {isComplete && (
                      <svg
                        className="h-3 w-3 text-green-600 dark:text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Action Button */}
            <button
              onClick={onReopen}
              className="mt-3 w-full rounded-lg bg-primary px-3 py-2 text-xs font-medium text-white hover:bg-opacity-90"
            >
              Continue Setup
            </button>
          </div>
        )}

        {/* Minimized View */}
        {isMinimized && (
          <div className="p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="h-3 w-3 rounded-full bg-primary"></div>
                <span className="text-xs text-gray-600 dark:text-gray-300">
                  {getCompletedSteps()}/{steps.length} complete
                </span>
              </div>
              <button
                onClick={onReopen}
                className="rounded bg-primary px-2 py-1 text-xs text-white hover:bg-opacity-90"
              >
                Resume
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnboardingProgressTracker;
