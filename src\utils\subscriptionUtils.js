/**
 * Subscription utilities for handling the new subscription system
 * Maps old subscription types (1,2,3) to new Stripe product IDs
 */

// Mapping from old subscription types to new product IDs
export const SUBSCRIPTION_MAPPING = {
  // Old system -> New system
  1: 3, // "Vocal Production Subscription" -> "The Studio" (ID 3)
  2: 2, // "Producer/Client Platform Subscription" -> "The Portal" (ID 2)  
  3: 4, // "Both (Vocal production and producer/client platform)" -> "Complete Suite" (ID 4)
};

// Reverse mapping for backward compatibility
export const REVERSE_SUBSCRIPTION_MAPPING = {
  2: 2, // "The Portal" -> equivalent to old subscription 2
  3: 1, // "The Studio" -> equivalent to old subscription 1
  4: 3, // "Complete Suite" -> equivalent to old subscription 3
};

// Product names mapping
export const PRODUCT_NAMES = {
  2: "The Portal",
  3: "The Studio", 
  4: "Complete Suite",
};

// Legacy subscription names
export const LEGACY_SUBSCRIPTION_NAMES = {
  1: "Vocal Production Subscription",
  2: "Producer/Client Platform Subscription", 
  3: "Both (Vocal production and producer/client platform)",
};

/**
 * Get the current user's subscription product ID
 * Handles both main members and sub-members
 * @param {Object} userDetails - User details from API
 * @returns {number|null} - Product ID or null if no subscription
 */
export const getUserSubscriptionProductId = (userDetails) => {
  if (!userDetails) return null;

  // Check if user is a sub-member
  const isSubMember = userDetails.main_user_details && !userDetails.main_user_details.is_self;
  
  if (isSubMember) {
    // For sub-members, use main member's plan_id
    return userDetails.main_user_details.plan_id || null;
  } else {
    // For main members, check if they have a plan_id from Stripe subscription
    if (userDetails.plan_id) {
      return userDetails.plan_id;
    }
    
    // Fallback: convert legacy subscription to new product ID
    const legacySubscription = userDetails.subscription;
    if (legacySubscription && SUBSCRIPTION_MAPPING[legacySubscription]) {
      return SUBSCRIPTION_MAPPING[legacySubscription];
    }
  }
  
  return null;
};

/**
 * Get subscription type for backward compatibility
 * Converts new product ID back to old subscription type (1,2,3)
 * @param {number} productId - Stripe product ID
 * @returns {number|null} - Legacy subscription type or null
 */
export const getSubscriptionTypeFromProductId = (productId) => {
  return REVERSE_SUBSCRIPTION_MAPPING[productId] || null;
};

/**
 * Check if user has access to features based on subscription
 * @param {number} productId - User's subscription product ID
 * @param {string} feature - Feature to check ('portal', 'studio', 'complete')
 * @returns {boolean} - Whether user has access
 */
export const hasSubscriptionAccess = (productId, feature) => {
  if (!productId) return false;
  
  switch (feature) {
    case 'studio':
      // Studio features available in "The Studio" (3) and "Complete Suite" (4)
      return productId === 3 || productId === 4;
    case 'portal':
      // Portal features available in "The Portal" (2) and "Complete Suite" (4)
      return productId === 2 || productId === 4;
    case 'complete':
      // Complete suite features only in "Complete Suite" (4)
      return productId === 4;
    default:
      return false;
  }
};

/**
 * Check if subscription requires advanced features (equivalent to old subscription > 1)
 * @param {number} productId - User's subscription product ID
 * @returns {boolean} - Whether subscription has advanced features
 */
export const hasAdvancedFeatures = (productId) => {
  // Advanced features available in "The Portal" (2) and "Complete Suite" (4)
  // This replaces the old parseInt(SubscriptionType) > 1 checks
  return productId === 2 || productId === 4;
};

/**
 * Get project limit based on subscription product ID
 * @param {number} productId - User's subscription product ID
 * @returns {number} - Project limit
 */
export const getProjectLimit = (productId) => {
  // This logic should match what's currently in AddProjectPage.jsx
  switch (productId) {
    case 2: // The Portal (was subscription 2)
      return 100;
    case 3: // The Studio (was subscription 1)
      return 50;
    case 4: // Complete Suite (was subscription 3)
      return 150;
    default:
      return 50; // Default limit
  }
};

/**
 * Get subscription display name
 * @param {number} productId - User's subscription product ID
 * @returns {string} - Display name
 */
export const getSubscriptionDisplayName = (productId) => {
  return PRODUCT_NAMES[productId] || 'Unknown Plan';
};
