import React, { useState, useEffect } from "react";
import { X, Calendar, DollarSign, Users, Package } from "lucide-react";
import MkdSDK from "Utils/MkdSDK";
import { GlobalContext, showToast } from "Src/globalContext";
import moment from "moment";

const PaymentReportModal = ({ isOpen, onClose }) => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [dateRange, setDateRange] = useState({
    startDate: moment().subtract(1, "year").format("YYYY-MM-DD"),
    endDate: moment().format("YYYY-MM-DD"),
  });

  useEffect(() => {
    if (isOpen) {
      fetchReport();
    }
  }, [isOpen]);

  const fetchReport = async (customDateRange = null) => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();

      const payload = {};
      if (customDateRange || (dateRange.startDate && dateRange.endDate)) {
        const range = customDateRange || dateRange;
        payload.startDate = range.startDate;
        payload.endDate = range.endDate;
      }

      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/subscription/company/payment-report",
        payload,
        "POST"
      );

      if (!response.error) {
        setReportData(response.report);
      } else {
        showToast(
          globalDispatch,
          "Failed to fetch payment report",
          4000,
          "error"
        );
      }
    } catch (error) {
      console.error("Error fetching payment report:", error);
      showToast(globalDispatch, "Error fetching payment report", 4000, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (field, value) => {
    const newDateRange = { ...dateRange, [field]: value };
    setDateRange(newDateRange);
  };

  const handleApplyDateFilter = () => {
    fetchReport(dateRange);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  if (!isOpen) return null;

  return (
    <div className="flex fixed inset-0 z-50 justify-center items-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className="relative max-h-[90vh] w-full max-w-4xl overflow-y-auto rounded-lg border border-strokedark bg-boxdark">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-strokedark">
          <h2 className="text-2xl font-bold text-white">Payment Report</h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-bodydark hover:bg-meta-4 hover:text-white"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Date Range Filter */}
          <div className="p-4 mb-6 rounded-lg border border-stroke bg-meta-4/20">
            <h3 className="flex gap-2 items-center mb-4 text-lg font-semibold text-white">
              <Calendar className="w-5 h-5" />
              Date Range Filter
            </h3>
            <div className="grid grid-cols-1 gap-4 items-end md:grid-cols-3">
              <div>
                <label className="block mb-2 text-sm font-medium text-white">
                  Start Date
                </label>
                <input
                  type="date"
                  value={dateRange.startDate}
                  onChange={(e) =>
                    handleDateRangeChange("startDate", e.target.value)
                  }
                  className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-3 py-2 outline-none transition focus:border-primary active:border-primary"
                />
              </div>
              <div>
                <label className="block mb-2 text-sm font-medium text-white">
                  End Date
                </label>
                <input
                  type="date"
                  value={dateRange.endDate}
                  onChange={(e) =>
                    handleDateRangeChange("endDate", e.target.value)
                  }
                  className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-3 py-2 outline-none transition focus:border-primary active:border-primary"
                />
              </div>
              <div>
                <button
                  onClick={handleApplyDateFilter}
                  disabled={loading}
                  className="px-4 py-2 w-full text-white rounded bg-primary hover:bg-opacity-90 disabled:opacity-50"
                >
                  {loading ? "Loading..." : "Apply Filter"}
                </button>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="w-12 h-12 rounded-full border-4 animate-spin border-primary border-t-transparent"></div>
            </div>
          ) : reportData ? (
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="p-6 rounded-lg border border-stroke bg-meta-4/20">
                  <div className="flex gap-3 items-center">
                    <div className="p-3 rounded-lg bg-success/20">
                      <DollarSign className="w-6 h-6 text-success" />
                    </div>
                    <div>
                      <p className="text-sm text-bodydark">Total Revenue</p>
                      <p className="text-2xl font-bold text-white">
                        {formatCurrency(reportData.totalAmount)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6 rounded-lg border border-stroke bg-meta-4/20">
                  <div className="flex gap-3 items-center">
                    <div className="p-3 rounded-lg bg-primary/20">
                      <Users className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <p className="text-sm text-bodydark">Active Members</p>
                      <p className="text-2xl font-bold text-white">
                        {Object.keys(reportData.memberBreakdown || {}).length}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="p-6 rounded-lg border border-stroke bg-meta-4/20">
                  <div className="flex gap-3 items-center">
                    <div className="p-3 rounded-lg bg-warning/20">
                      <Package className="w-6 h-6 text-warning" />
                    </div>
                    <div>
                      <p className="text-sm text-bodydark">Special Items</p>
                      <p className="text-2xl font-bold text-white">
                        {formatCurrency(
                          reportData.specialItems?.totalAmount || 0
                        )}
                      </p>
                      <p className="text-xs text-bodydark">
                        {reportData.specialItems?.itemCount || 0} items
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Member Breakdown */}
              {reportData.memberBreakdown &&
                Object.keys(reportData.memberBreakdown).length > 0 && (
                  <div className="p-6 rounded-lg border border-stroke bg-meta-4/20">
                    <h3 className="mb-4 text-lg font-semibold text-white">
                      Member Revenue Breakdown
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-strokedark">
                            <th className="px-4 py-3 text-left text-white">
                              Member Name
                            </th>
                            <th className="px-4 py-3 text-left text-white">
                              Member ID
                            </th>
                            <th className="px-4 py-3 text-right text-white">
                              Total Amount
                            </th>
                            <th className="px-4 py-3 text-right text-white">
                              Percentage
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.values(reportData.memberBreakdown).map(
                            (member) => {
                              const percentage = (
                                (member.totalAmount / reportData.totalAmount) *
                                100
                              ).toFixed(1);
                              return (
                                <tr
                                  key={member.memberId}
                                  className="border-b border-strokedark/50"
                                >
                                  <td className="px-4 py-3 text-white">
                                    {member.memberName}
                                  </td>
                                  <td className="px-4 py-3 text-bodydark">
                                    #{member.memberId}
                                  </td>
                                  <td className="px-4 py-3 font-semibold text-right text-white">
                                    {formatCurrency(member.totalAmount)}
                                  </td>
                                  <td className="px-4 py-3 text-right text-bodydark">
                                    {percentage}%
                                  </td>
                                </tr>
                              );
                            }
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

              {/* Date Range Info */}
              <div className="p-4 rounded-lg border border-stroke bg-meta-4/10">
                <p className="text-sm text-bodydark">
                  <strong>Report Period:</strong>{" "}
                  {reportData.dateRange?.startDate
                    ? moment(reportData.dateRange.startDate).format(
                        "MMM DD, YYYY"
                      )
                    : moment().subtract(1, "year").format("MMM DD, YYYY")}{" "}
                  -{" "}
                  {reportData.dateRange?.endDate
                    ? moment(reportData.dateRange.endDate).format(
                        "MMM DD, YYYY"
                      )
                    : moment().format("MMM DD, YYYY")}
                </p>
                {/* <p className="mt-1 text-sm text-bodydark">
                  <strong>Company ID:</strong> #{reportData.companyId}
                </p> */}
              </div>
            </div>
          ) : (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <p className="text-lg text-white">No report data available</p>
                <p className="text-bodydark">Try adjusting the date range</p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex gap-3 justify-end p-6 border-t border-strokedark">
          <button
            onClick={onClose}
            className="px-6 py-2 text-white rounded-lg border border-stroke hover:bg-meta-4"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentReportModal;
