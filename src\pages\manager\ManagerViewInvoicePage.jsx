import React, { useState, useEffect, useContext } from "react";
import { useParams, useNavigate } from "react-router-dom";
import MkdSDK from "../../utils/MkdSDK";
import { GlobalContext } from "../../globalContext";
import moment from "moment";
import { ClipLoader } from "react-spinners";
import { ArrowLeft, RefreshCw, Download, Edit, FileText } from "lucide-react";
import RefundModal from "../../components/Modal/RefundModal";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoiceQuotePDF from "../../components/Invoice/InvoiceQuotePDF";

const ManagerViewInvoicePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [loading, setLoading] = useState(true);
  const [invoice, setInvoice] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [totalPrice, setTotalPrice] = useState(0);
  const [depositPaid, setDepositPaid] = useState(0);
  const [totalPaid, setTotalPaid] = useState(0);
  const [balanceDue, setBalanceDue] = useState(0);
  const [isRefundModalOpen, setIsRefundModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [mixSeasons, setMixSeasons] = useState({});

  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        const sdk = new MkdSDK();
        const result = await sdk.getInvoiceById(id);
        console.log("Invoice details:", result);
        setInvoice(result);

        // Calculate total price
        if (result?.items?.length > 0) {
          const total = result.items.reduce((sum, item) => {
            return sum + parseFloat(item.total || 0);
          }, 0);
          setTotalPrice(total);
        }

        // Calculate total paid amount from payment history
        if (result?.payments?.length > 0) {
          const paid = result.payments.reduce((sum, payment) => {
            // For check payments, always consider them as succeeded
            if (
              payment.payment_method === "check" ||
              payment.status === "succeeded"
            ) {
              // Only count if not refunded
              if (payment.refunded !== 1) {
                // Convert from cents to dollars
                return sum + parseFloat(payment.amount || 0) / 100;
              }
            }
            return sum;
          }, 0);
          setTotalPaid(paid);
          setDepositPaid(paid); // Update deposit paid based on actual payments

          // Calculate balance due
          const totalInvoiceAmount = parseFloat(result.invoice?.total || 0);
          setBalanceDue(totalInvoiceAmount - paid);
        } else {
          setTotalPaid(0);
          setDepositPaid(0);
          setBalanceDue(parseFloat(result.invoice?.total || 0));
        }

        // Fetch company info to map producer IDs to names
        await fetchCompanyInfo();

        if (result?.user_id) {
          const userResult = await sdk.getUserDetails(result.user_id);
          setUserDetails(userResult);
        }

        // Fetch mix seasons for each producer in the invoice items
        if (result.items && result.items.length > 0) {
          const producerIds = new Set();

          // Collect all unique producer IDs
          result.items.forEach((item) => {
            if (item.producer) {
              producerIds.add(item.producer);
            }
          });

          // Fetch mix seasons for each unique producer
          const promises = [];
          producerIds.forEach((producerId) => {
            promises.push(fetchMixSeasonsForProducer(producerId));
          });

          await Promise.all(promises);
        }
      } catch (error) {
        console.error("Error fetching invoice:", error);
        globalDispatch({
          type: "SHOW_TOAST",
          payload: {
            text: "Failed to fetch invoice details",
            type: "error",
          },
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInvoiceDetails();
  }, [id, globalDispatch, refreshTrigger]);

  // Function to handle refund completion
  const handleRefundComplete = () => {
    // Increment the refresh trigger to reload the data
    setRefreshTrigger((prev) => prev + 1);
  };

  const fetchCompanyInfo = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        "/v3/api/custom/equality_record/user/company/info",
        {},
        "GET"
      );

      console.log("Company info response:", response);

      if (!response.error && response.company) {
        setCompanyInfo(response);
      } else {
        console.error(
          "Error fetching company info:",
          response.message || "Unknown error"
        );
      }
    } catch (error) {
      console.error("Error fetching company info:", error);
    }
  };

  const fetchMixSeasonsForProducer = async (producerId) => {
    if (!producerId) return;

    try {
      const sdk = new MkdSDK();
      const response = await sdk.callRawAPI(
        `/v4/api/records/mix_season?filter=user_id,eq,${producerId}`,
        {},
        "GET"
      );

      if (!response.error && response.list) {
        // Sort seasons in ascending order
        const sortedSeasons = response.list.sort((a, b) => {
          // First try to sort by name if it contains numbers
          const aNum = parseInt(a.name.replace(/\D/g, ""));
          const bNum = parseInt(b.name.replace(/\D/g, ""));

          if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum;
          }

          // Fall back to alphabetical sort
          return a.name.localeCompare(b.name);
        });

        // Update the mixSeasons state with the new data for this producer
        setMixSeasons((prev) => ({
          ...prev,
          [producerId]: sortedSeasons,
        }));
      }
    } catch (error) {
      console.error(
        `Error fetching mix seasons for producer ${producerId}:`,
        error
      );
    }
  };

  // Function to get producer name from ID
  const getProducerName = (producerId) => {
    if (!producerId) return "N/A";
    if (!companyInfo) return `Producer ID: ${producerId}`;

    // Check in manager
    if (companyInfo.company.manager.id === parseInt(producerId)) {
      return `${companyInfo.company.manager.first_name} ${companyInfo.company.manager.last_name}`;
    }

    // Check in members
    const member = companyInfo.company.members.find(
      (member) => member.id === parseInt(producerId)
    );
    if (member) {
      return `${member.first_name} ${member.last_name}`;
    }

    return `Producer ID: ${producerId}`;
  };

  // Function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return moment(dateString).format("MMM DD, YYYY");
  };

  // Function to format currency
  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return "$0.00";
    // Convert from cents to dollars if it's a payment amount
    return `$${parseFloat(amount).toFixed(2)}`;
  };

  // Function to format payment amount (convert from cents to dollars)
  const formatPaymentAmount = (amount) => {
    if (amount === null || amount === undefined) return "$0.00";
    // Convert from cents to dollars
    return `$${(parseFloat(amount || 0) / 100).toFixed(2)}`;
  };

  // Function to get mix season name from ID
  const getMixSeasonName = (mixSeasonId, producerId) => {
    if (!mixSeasonId || !producerId || !mixSeasons[producerId]) {
      return "Unknown";
    }

    const season = mixSeasons[producerId].find(
      (season) => season.id === parseInt(mixSeasonId)
    );

    return season ? season.name : "Unknown";
  };

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <ClipLoader color="#fff" size={30} />
      </div>
    );
  }

  if (!invoice) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-danger">Invoice Not Found</h2>
        </div>
      </div>
    );
  }

  return (
    <div className="rounded-lg bg-boxdark p-6">
      {/* Company Information Header */}
      <div className="mb-6 flex items-center justify-between rounded-lg bg-meta-4 p-4">
        <div className="flex items-center gap-4">
          {companyInfo?.company?.manager?.license_company_logo &&
          companyInfo.company.manager.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.manager.license_company_logo}
              alt={companyInfo.company.manager.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : companyInfo?.company?.main_member?.license_company_logo &&
            companyInfo.company.main_member.license_company_logo !== "null" ? (
            <img
              src={companyInfo.company.main_member.license_company_logo}
              alt={
                companyInfo.company.main_member.company_name || "Company Logo"
              }
              className="h-16 w-auto object-contain"
            />
          ) : userDetails?.company_logo &&
            userDetails.company_logo !== "null" ? (
            <img
              src={userDetails.company_logo}
              alt={userDetails?.company_name || "Company Logo"}
              className="h-16 w-auto object-contain"
            />
          ) : null}
          <div>
            <h2 className="text-xl font-bold text-white">
              {companyInfo?.company?.manager?.company_name &&
              companyInfo.company.manager.company_name !== "null"
                ? companyInfo.company.manager.company_name
                : companyInfo?.company?.main_member?.company_name &&
                  companyInfo.company.main_member.company_name !== "null"
                ? companyInfo.company.main_member.company_name
                : userDetails?.company_name || "Your Company"}
            </h2>
            <p className="text-sm text-bodydark2">
              {companyInfo?.company?.manager?.office_email &&
              companyInfo.company.manager.office_email !== "null"
                ? companyInfo.company.manager.office_email
                : companyInfo?.company?.main_member?.office_email &&
                  companyInfo.company.main_member.office_email !== "null"
                ? companyInfo.company.main_member.office_email
                : userDetails?.office_email || ""}
            </p>
          </div>
        </div>
        <div className="text-right">
          <h2 className="text-2xl font-bold text-white">
            {invoice.invoice?.status === "quote" ? "QUOTE" : "INVOICE"}
          </h2>
          <div className="mt-2 flex items-center gap-2">
            {/* <span
              className={`rounded px-2.5 py-0.5 text-sm font-medium ${
                invoice.invoice?.status === "paid"
                  ? "bg-success/10 text-success"
                  : invoice.invoice?.status === "pending"
                  ? "bg-warning/10 text-warning"
                  : "bg-danger/10 text-danger"
              }`}
            >
              {invoice.invoice?.status}
            </span> */}
            {/* {invoice.invoice?.payment_status && (
              <span
                className={`rounded px-2.5 py-0.5 text-sm font-medium ${
                  invoice.invoice?.payment_status === "succeeded"
                    ? "bg-success/10 text-success"
                    : "bg-warning/10 text-warning"
                }`}
              >
                {invoice.invoice?.payment_status?.replace(/_/g, " ")}
              </span>
            )} */}
          </div>
        </div>
      </div>

      {/* Header with back button and action buttons */}
      <div className="mb-6 flex items-center justify-between">
        <button
          onClick={() => navigate("/manager/invoices")}
          className="flex items-center gap-2 text-white hover:text-primary"
        >
          <ArrowLeft className="h-5 w-5" />
          Back to Invoices
        </button>
        <h2 className="text-2xl font-bold text-white">
          {invoice.invoice?.status === "quote" ? "Quote" : "Invoice"} #
          {invoice.invoice?.id}
        </h2>
        <div className="flex items-center gap-3">
          <p className="text-sm text-bodydark2">
            Created: {moment(invoice.invoice?.create_at).format("MMM DD, YYYY")}
          </p>
          <div className="flex items-center gap-2">
            {/* Export PDF Button */}
            <PDFDownloadLink
              document={
                <InvoiceQuotePDF
                  data={{
                    selectedClientId: invoice.invoice?.client_id,
                    newClientData: {},
                    invoiceDates: {
                      invoiceDate: invoice.invoice?.invoice_date,
                      invoiceDueDate: invoice.invoice?.due_date,
                    },
                    invoiceData: {
                      invoiceNumber: invoice.invoice?.id,
                      items: invoice.items?.map((item) => ({
                        price: item.price,
                        quantity: item.quantity || 1,
                        discount: item.discount || 0,
                        mixDate: item.mix_date,
                        teamName: item.team_name,
                        division: item.division,
                        producer: item.producer,
                        mixType:
                          item.description?.split(" - ")[1] || item.description,
                        musicSurveyDue: item.music_survey_due,
                        routineSubmissionDue: item.routine_submission_due,
                        estimatedCompletion: item.estimated_completion,
                        isSpecial: item.is_special === 1,
                        name:
                          item.is_special === 1
                            ? item.description || "Additional Charge"
                            : "",
                      })),
                      notes: invoice.invoice?.notes,
                      termsAndConditions: invoice.invoice?.terms_and_conditions,
                      depositPercentage:
                        invoice.invoice?.deposit_percentage || 0,
                      isQuote: false,
                    },
                    clientDetails: {
                      program: invoice.invoice?.program,
                      email: invoice.invoice?.client_email,
                    },
                  }}
                  userDetails={userDetails}
                  companyInfo={companyInfo}
                />
              }
              fileName={`invoice-${invoice.invoice?.id}-${moment().format(
                "YYYY-MM-DD"
              )}.pdf`}
              className="flex items-center gap-1 rounded bg-meta-4 px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              {({ loading: pdfLoading }) => (
                <>
                  <Download className="h-4 w-4" />
                  {pdfLoading ? "Generating..." : "Export PDF"}
                </>
              )}
            </PDFDownloadLink>

            {/* Save as Quote Button */}
            <PDFDownloadLink
              document={
                <InvoiceQuotePDF
                  data={{
                    selectedClientId: invoice.invoice?.client_id,
                    newClientData: {},
                    invoiceDates: {
                      invoiceDate: invoice.invoice?.invoice_date,
                      invoiceDueDate: invoice.invoice?.due_date,
                    },
                    invoiceData: {
                      invoiceNumber: invoice.invoice?.id,
                      items: invoice.items?.map((item) => ({
                        price: item.price,
                        quantity: item.quantity || 1,
                        discount: item.discount || 0,
                        mixDate: item.mix_date,
                        teamName: item.team_name,
                        division: item.division,
                        producer: item.producer,
                        mixType:
                          item.description?.split(" - ")[1] || item.description,
                        musicSurveyDue: item.music_survey_due,
                        routineSubmissionDue: item.routine_submission_due,
                        estimatedCompletion: item.estimated_completion,
                        isSpecial: item.is_special === 1,
                        name:
                          item.is_special === 1
                            ? item.description || "Additional Charge"
                            : "",
                      })),
                      notes: invoice.invoice?.notes,
                      termsAndConditions: invoice.invoice?.terms_and_conditions,
                      depositPercentage:
                        invoice.invoice?.deposit_percentage || 0,
                      isQuote: true,
                    },
                    clientDetails: {
                      program: invoice.invoice?.program,
                      email: invoice.invoice?.client_email,
                    },
                  }}
                  userDetails={userDetails}
                  companyInfo={companyInfo}
                />
              }
              fileName={`quote-${invoice.invoice?.id}-${moment().format(
                "YYYY-MM-DD"
              )}.pdf`}
              className="flex items-center gap-1 rounded bg-meta-5 px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              {({ loading: pdfLoading }) => (
                <>
                  <Download className="h-4 w-4" />
                  {pdfLoading ? "Generating..." : "Save as Quote"}
                </>
              )}
            </PDFDownloadLink>

            {/* Edit Button */}
            <button
              onClick={() => {
                // Store the invoice ID in localStorage to be picked up by ManagerInvoicePage
                localStorage.setItem("editInvoiceId", invoice.invoice?.id);
                navigate("/manager/invoices");
              }}
              className="flex items-center gap-1 rounded bg-primary px-3 py-1.5 text-sm font-medium text-white hover:bg-opacity-80"
            >
              <Edit className="h-4 w-4" />
              Edit
            </button>
          </div>
        </div>
      </div>

      {/* Client Information */}
      <div className="mb-3 pb-2">
        <h3 className="mb-2 text-base font-semibold text-white">
          Client Information
        </h3>
        <div className="grid grid-cols-2 gap-6 rounded-lg bg-meta-4/20 p-4">
          <div>
            <p className="text-sm text-bodydark2">Name</p>
            <p className="mt-1 font-medium">{invoice.invoice?.client_name}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Email</p>
            <p className="mt-1 font-medium">{invoice.invoice?.client_email}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Program</p>
            <p className="mt-1 font-medium">{invoice.invoice?.program}</p>
          </div>
          <div>
            <p className="text-sm text-bodydark2">Invoice Date</p>
            <p className="mt-1 font-medium">
              {formatDate(invoice.invoice?.invoice_date)}
            </p>
          </div>
        </div>
      </div>

      {/* Invoice Items */}
      <div className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">Invoice Items</h3>
        </div>
        <div className="custom-overflow min-h-[140px] overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Team Name
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Division
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Producer
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Mix Type/Season
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Survey Due/&nbsp;Submission Due/ Est. Completion
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Price
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Discount
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Total
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-white">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoice.items?.map((item, index) => (
                <tr
                  key={index}
                  className={`border-b border-stroke/50 text-[12px] hover:bg-primary/5 ${
                    item.is_special === 1 ? "bg-meta-4/20" : ""
                  }`}
                >
                  <td className="whitespace-nowrap px-4 py-3">
                    {item.is_special === 1 ? item.special_type : "Normal"}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {formatDate(item.mix_date)}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {item.team_name}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {item.division}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {getProducerName(item.producer)}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    <div>
                      <p>{item.description || "N/A"}</p>
                      <p className="text-xs text-bodydark2">
                        {item.mix_season_id
                          ? getMixSeasonName(item.mix_season_id, item.producer)
                          : item.mix_season || "N/A"}
                      </p>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center">
                        <span>{formatDate(item.music_survey_due)}</span>
                      </div>
                      <div className="flex items-center">
                        <span>{formatDate(item.routine_submission_due)}</span>
                      </div>
                      <div className="flex items-center">
                        <span>{formatDate(item.estimated_completion)}</span>
                      </div>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {formatCurrency(item.price)}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3">
                    {item.discount ? `${item.discount}%` : "0%"}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3 text-right">
                    {formatCurrency(item.total)}
                  </td>
                  <td className="whitespace-nowrap px-4 py-3 text-center">
                    <select
                      className="rounded border border-strokedark bg-boxdark px-2 py-1 text-xs font-medium text-white"
                      value={item.status || "5"}
                      onChange={async (e) => {
                        try {
                          const sdk = new MkdSDK();
                          const newStatus = e.target.value;

                          // Update the item status
                          await sdk.callRawAPI(
                            `/v3/api/custom/equality_record/subscription/invoice-item/${item.id}`,
                            { status: newStatus },
                            "PUT"
                          );

                          // Refresh the data
                          setRefreshTrigger((prev) => prev + 1);

                          // Show success message
                          globalDispatch({
                            type: "SHOW_TOAST",
                            payload: {
                              text: "Status updated successfully",
                              type: "success",
                            },
                          });
                        } catch (error) {
                          console.error("Error updating status:", error);
                          globalDispatch({
                            type: "SHOW_TOAST",
                            payload: {
                              text: "Failed to update status",
                              type: "error",
                            },
                          });
                        }
                      }}
                    >
                      <option value="1">Complete</option>
                      <option value="2">Deposit Paid</option>
                      <option value="3">Paid in Full</option>
                      <option value="4">Awaiting Edit</option>
                      <option value="5">Unpaid</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Information */}
      <div className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">
            Payment Details
          </h3>
        </div>
        <div className="rounded-lg bg-meta-4/20 p-4">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
            <div>
              <p className="text-sm text-bodydark2">Invoice Total</p>
              <p className="mt-1 font-medium">
                {formatCurrency(invoice.invoice?.total)}
              </p>
            </div>
            <div>
              <p className="text-sm text-bodydark2">Amount Paid</p>
              <p className="mt-1 font-medium">{formatCurrency(totalPaid)}</p>
            </div>
            <div>
              <p className="text-sm text-bodydark2">Balance Due</p>
              <p className="mt-1 text-lg font-medium text-primary">
                {formatCurrency(balanceDue)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Payment History */}
      <div className="mb-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-base font-semibold text-white">
            Payment History
          </h3>
        </div>
        <div className="custom-overflow overflow-x-auto">
          <table className="relative w-full table-auto">
            <thead className="bg-meta-4">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Method
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider text-white">
                  Status
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Amount
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Fee
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider text-white">
                  Total
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider text-white">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="text-white">
              {invoice.payments?.length > 0 ? (
                invoice.payments.map((payment) => (
                  <tr
                    key={payment.id}
                    className={`border-b border-stroke/50 text-[12px] hover:bg-primary/5 ${
                      payment.refunded === 1 ? "bg-danger/5" : ""
                    }`}
                  >
                    <td className="whitespace-nowrap px-4 py-3">
                      {formatDate(payment.create_at)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 capitalize">
                      {payment.payment_method}
                      {payment.payment_method === "check" &&
                        payment.check_attachment_url && (
                          <a
                            href={payment.check_attachment_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="ml-2 text-primary hover:underline"
                          >
                            (View Check)
                          </a>
                        )}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3">
                      {payment.refunded === 1 ? (
                        <span className="rounded bg-danger/10 px-2 py-1 text-xs font-medium text-danger">
                          Refunded
                        </span>
                      ) : (
                        <span
                          className={`rounded px-2 py-1 text-xs font-medium ${
                            payment.status === "succeeded" ||
                            payment.payment_method === "check"
                              ? "bg-success/10 text-success"
                              : "bg-warning/10 text-warning"
                          }`}
                        >
                          {payment.payment_method === "check"
                            ? "succeeded"
                            : payment.status.replace(/_/g, " ")}
                        </span>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      {formatPaymentAmount(payment.amount)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      {formatPaymentAmount(payment.stripe_fee)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-right">
                      {formatPaymentAmount(payment.total_charged)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-3 text-center">
                      {payment.refunded !== 1 &&
                        (payment.status === "succeeded" ||
                          payment.payment_method === "check") && (
                          <button
                            onClick={() => {
                              setSelectedPayment(payment);
                              setIsRefundModalOpen(true);
                            }}
                            className="rounded bg-danger px-2 py-1 text-xs font-medium text-white hover:bg-danger/70"
                          >
                            Refund
                          </button>
                        )}
                      {payment.refunded === 1 && payment.refund_id && (
                        <span className="text-xs text-bodydark2">
                          Refund ID: {payment.refund_id}
                        </span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={6}
                    className="px-4 py-3 text-center text-bodydark2"
                  >
                    No payment records found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Notes and Terms */}
      {(invoice.invoice?.notes || invoice.invoice?.terms_and_conditions) && (
        <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
          {invoice.invoice?.notes && (
            <div className="rounded-lg bg-meta-4/20 p-4">
              <h3 className="mb-2 text-base font-semibold text-white">Notes</h3>
              <p className="whitespace-pre-wrap text-bodydark2">
                {invoice.invoice?.notes}
              </p>
            </div>
          )}
          {invoice.invoice?.terms_and_conditions && (
            <div className="rounded-lg bg-meta-4/20 p-4">
              <h3 className="mb-2 text-base font-semibold text-white">
                Terms and Conditions
              </h3>
              <div
                className="whitespace-pre-wrap text-bodydark2"
                dangerouslySetInnerHTML={{
                  __html: invoice.invoice?.terms_and_conditions,
                }}
              />
            </div>
          )}
        </div>
      )}

      {/* Service Agreement Section */}
      <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
        <h3 className="mb-2 text-base font-semibold text-white">
          Service Agreement
        </h3>
        {invoice.invoice?.attachment_url ? (
          <div className="flex items-center gap-4">
            <p className="text-bodydark2">
              Service agreement has been signed and is available for download.
            </p>
            <a
              href={invoice.invoice.attachment_url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 rounded bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-opacity-90"
            >
              <Download className="h-4 w-4" />
              Download Service Agreement PDF
            </a>
          </div>
        ) : (
          <p className="text-bodydark2">
            Service agreement is not available yet.
          </p>
        )}
      </div>

      {/* Check Uploads Section */}
      {invoice.invoice?.checks &&
        (() => {
          try {
            const checks = JSON.parse(invoice.invoice.checks);
            const validChecks = checks.filter(
              (check) => check.url && check.id && check.uploadDate
            );
            return (
              validChecks.length > 0 && (
                <div className="mb-6 rounded-lg bg-meta-4/20 p-4">
                  <h3 className="mb-4 text-base font-semibold text-white">
                    Check Uploads
                  </h3>
                  <div className="space-y-2">
                    {validChecks.map((check) => (
                      <div
                        key={check.id}
                        className="flex items-center justify-between rounded border border-stroke/50 bg-boxdark-2/40 p-3"
                      >
                        <div className="flex items-center gap-3">
                          <FileText className="h-4 w-4 text-primary" />
                          <div>
                            <p className="text-sm font-medium text-white">
                              {check.filename || "Unknown File"}
                            </p>
                            <p className="text-xs text-bodydark2">
                              Uploaded:{" "}
                              {moment(check.uploadDate).format(
                                "MMM DD, YYYY HH:mm"
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <a
                            href={check.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 rounded bg-meta-5 px-3 py-1 text-xs font-medium text-white hover:bg-opacity-90"
                          >
                            <FileText className="h-3 w-3" />
                            View
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )
            );
          } catch (error) {
            console.error("Error parsing checks data:", error);
            return null;
          }
        })()}

      {/* Refund Modal */}
      <RefundModal
        isOpen={isRefundModalOpen}
        onClose={() => setIsRefundModalOpen(false)}
        payment={selectedPayment}
        onRefundComplete={handleRefundComplete}
        formatCurrency={formatPaymentAmount}
      />
    </div>
  );
};

export default ManagerViewInvoicePage;
