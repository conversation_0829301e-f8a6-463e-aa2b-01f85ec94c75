import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { Link, useNavigate } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";

let sdk = new MkdSDK();

const schema = yup
  .object({
    email: yup.string().email().required(),
    password: yup.string().min(8).required(),
    confirmPassword: yup
      .string()
      .oneOf([yup.ref("password"), null], "Passwords must match")
      .required(),
    first_name: yup.string().required(),
    last_name: yup.string().required(),
  })
  .required();

const MemberRegisterPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [submitLoading, setSubmitLoading] = useState(false);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    try {
      setSubmitLoading(true);
      const result = await sdk.register(
        data.email,
        data.password,
        "member",
        data.first_name,
        data.last_name
      );

      if (!result.error) {
        // Send verification email
        try {
          const verificationResult = await sdk.sendVerificationEmail(
            data.email
          );
          if (!verificationResult.error) {
            showToast(
              globalDispatch,
              "Registration successful! Please check your email for verification.",
              4000,
              "success"
            );

            // Store user data temporarily for verification flow
            localStorage.setItem(
              "pending_verification_user",
              JSON.stringify({
                email: data.email,
                user_id: result.user_id || result.id,
              })
            );

            // Navigate to email verification page
            navigate("/member/verify-email");
          } else {
            throw new Error(
              verificationResult.message || "Failed to send verification email"
            );
          }
        } catch (verificationError) {
          console.error("Verification email error:", verificationError);
          showToast(
            globalDispatch,
            "Registration successful, but failed to send verification email. Please contact support.",
            6000,
            "warning"
          );
          // Still navigate to verification page so user can try to resend
          localStorage.setItem(
            "pending_verification_user",
            JSON.stringify({
              email: data.email,
              user_id: result.user_id || result.id,
            })
          );
          navigate("/member/verify-email");
        }
      } else {
        setSubmitLoading(false);
        if (result.validation) {
          const keys = Object.keys(result.validation);
          for (let i = 0; i < keys.length; i++) {
            const field = keys[i];
            setError(field, {
              type: "manual",
              message: result.validation[field],
            });
          }
        } else if (result.message) {
          showToast(globalDispatch, result.message, 4000, "error");
        }
      }
    } catch (error) {
      setSubmitLoading(false);
      console.log("Error", error);
      showToast(
        globalDispatch,
        error.message || "Registration failed. Please try again.",
        4000,
        "error"
      );
    }
  };

  return (
    <>
      <div className="shadow-default rounded-sm border border-stroke bg-white dark:border-strokedark dark:bg-boxdark">
        <div className="flex flex-wrap items-center">
          <div className="hidden w-full xl:block xl:w-1/2">
            <div className="py-17.5 px-26 text-center">
              <Link className="mb-5.5 inline-block" to="/">
                <img className="hidden dark:block" src="/logo.png" alt="Logo" />
                <img className="dark:hidden" src="/logo.png" alt="Logo" />
              </Link>

              <p className="2xl:px-20">
                Join thousands of music professionals who trust our platform for
                their projects.
              </p>

              <span className="mt-15 inline-block">
                <img src="/illustration-03.svg" alt="illustration" />
              </span>
            </div>
          </div>

          <div className="w-full border-stroke xl:w-1/2 xl:border-l-2 dark:border-strokedark">
            <div className="sm:p-12.5 xl:p-17.5 w-full p-4">
              <span className="mb-1.5 block font-medium">Start for free</span>
              <h2 className="mb-9 text-2xl font-bold text-black sm:text-title-xl2 dark:text-white">
                Create Your Account
              </h2>

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="mb-4 flex gap-4">
                  <div className="w-1/2">
                    <label className="mb-2.5 block font-medium text-black dark:text-white">
                      First Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Enter your first name"
                        {...register("first_name")}
                        className={`w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary ${
                          errors.first_name?.message
                            ? "border-red-500"
                            : "border-stroke dark:border-form-strokedark"
                        }`}
                      />
                      {errors.first_name?.message && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.first_name.message}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="w-1/2">
                    <label className="mb-2.5 block font-medium text-black dark:text-white">
                      Last Name
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Enter your last name"
                        {...register("last_name")}
                        className={`w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary ${
                          errors.last_name?.message
                            ? "border-red-500"
                            : "border-stroke dark:border-form-strokedark"
                        }`}
                      />
                      {errors.last_name?.message && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.last_name.message}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <label className="mb-2.5 block font-medium text-black dark:text-white">
                    Email
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      placeholder="Enter your email"
                      {...register("email")}
                      className={`w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary ${
                        errors.email?.message
                          ? "border-red-500"
                          : "border-stroke dark:border-form-strokedark"
                      }`}
                    />
                    {errors.email?.message && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.email.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mb-4">
                  <label className="mb-2.5 block font-medium text-black dark:text-white">
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      placeholder="Enter your password"
                      {...register("password")}
                      className={`w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary ${
                        errors.password?.message
                          ? "border-red-500"
                          : "border-stroke dark:border-form-strokedark"
                      }`}
                    />
                    {errors.password?.message && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.password.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mb-6">
                  <label className="mb-2.5 block font-medium text-black dark:text-white">
                    Confirm Password
                  </label>
                  <div className="relative">
                    <input
                      type="password"
                      placeholder="Confirm your password"
                      {...register("confirmPassword")}
                      className={`w-full rounded-lg border border-stroke bg-transparent py-4 pl-6 pr-10 outline-none focus:border-primary focus-visible:shadow-none dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary ${
                        errors.confirmPassword?.message
                          ? "border-red-500"
                          : "border-stroke dark:border-form-strokedark"
                      }`}
                    />
                    {errors.confirmPassword?.message && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.confirmPassword.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="mb-5">
                  <input
                    type="submit"
                    value={
                      submitLoading ? "Creating Account..." : "Create Account"
                    }
                    disabled={submitLoading}
                    className="w-full cursor-pointer rounded-lg border border-primary bg-primary p-4 text-white transition hover:bg-opacity-90 disabled:opacity-50"
                  />
                  {submitLoading && (
                    <div className="mt-2 flex justify-center">
                      <ClipLoader color="#fff" size={20} />
                    </div>
                  )}
                </div>

                <div className="mt-6 text-center">
                  <p>
                    Already have an account?{" "}
                    <Link to="/member/login" className="text-primary">
                      Sign In
                    </Link>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MemberRegisterPage;
