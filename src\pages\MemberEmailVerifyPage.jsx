import React, { useState, useEffect } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { GlobalContext, showToast } from "../globalContext";
import { AuthContext } from "../authContext";
import MkdSDK from "../utils/MkdSDK";
import { ClipLoader } from "react-spinners";

let sdk = new MkdSDK();

const MemberEmailVerifyPage = () => {
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { dispatch: authDispatch } = React.useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState("checking"); // checking, verifying, verified, unverified, error
  const [userEmail, setUserEmail] = useState("");
  const [resendLoading, setResendLoading] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    checkVerificationStatus();
  }, []);

  const checkVerificationStatus = async () => {
    try {
      // Check if coming from email verification link with token
      const token = searchParams.get("token");
      if (token) {
        setVerificationStatus("verifying");

        try {
          // Use the correct verification endpoint
          const result = await sdk.callRawAPI(
            `/v2/api/lambda/verify-email?token=${token}`,
            {},
            "GET"
          );

          if (!result.error) {
            setVerificationStatus("verified");
            showToast(
              globalDispatch,
              "Email verified successfully! Redirecting to subscription...",
              3000,
              "success"
            );

            // Clear pending user data
            localStorage.removeItem("pending_verification_user");

            // Login the user if login data is provided
            if (result.user) {
              authDispatch({
                type: "LOGIN",
                payload: result,
              });
            }

            // Always redirect to dashboard - onboarding will be handled there
            setTimeout(() => {
              navigate("/member/dashboard");
            }, 1500);
          } else {
            throw new Error(result.message || "Verification failed");
          }
        } catch (verifyError) {
          console.error("Verification error:", verifyError);
          setVerificationStatus("error");
          showToast(
            globalDispatch,
            verifyError.message ||
              "Email verification failed. Please try again.",
            4000,
            "error"
          );
        }
        return;
      }

      // Check for pending verification user (when user is on verification page without token)
      const pendingUser = localStorage.getItem("pending_verification_user");
      if (pendingUser) {
        const userData = JSON.parse(pendingUser);
        setUserEmail(userData.email);
        setVerificationStatus("unverified");
      } else {
        // No pending user and no token, redirect to login
        navigate("/member/login");
      }
    } catch (error) {
      console.error("Error checking verification status:", error);
      setVerificationStatus("error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setResendLoading(true);
      const pendingUser = localStorage.getItem("pending_verification_user");
      if (pendingUser) {
        const userData = JSON.parse(pendingUser);

        // Use the correct verification email API
        const result = await sdk.sendVerificationEmail(userData.email);

        if (!result.error) {
          showToast(
            globalDispatch,
            "Verification email sent! Please check your inbox.",
            4000,
            "success"
          );
        } else {
          showToast(
            globalDispatch,
            result.message ||
              "Failed to resend verification email. Please try again.",
            4000,
            "error"
          );
        }
      }
    } catch (error) {
      console.error("Error resending verification:", error);
      showToast(
        globalDispatch,
        error.message ||
          "Failed to resend verification email. Please try again.",
        4000,
        "error"
      );
    } finally {
      setResendLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <ClipLoader color="#3B82F6" size={50} />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-sm border shadow-default border-stroke dark:border-strokedark dark:bg-boxdark">
      <div className="flex flex-wrap items-center">
        <div className="hidden w-full xl:block xl:w-1/2">
          <div className="py-17.5 px-26 text-center">
            <Link className="mb-5.5 inline-block" to="/">
              <img className="hidden dark:block" src="/logo.png" alt="Logo" />
              <img className="dark:hidden" src="/logo.png" alt="Logo" />
            </Link>

            <p className="2xl:px-20">
              We've sent a verification link to your email address.
            </p>

            <span className="inline-block mt-15">
              <img src="/illustration-03.svg" alt="illustration" />
            </span>
          </div>
        </div>

        <div className="w-full border-stroke xl:w-1/2 xl:border-l-2 dark:border-strokedark">
          <div className="sm:p-12.5 xl:p-17.5 w-full p-4">
            {verificationStatus === "verifying" && (
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  <div className="flex justify-center items-center w-16 h-16 bg-blue-100 rounded-full">
                    <ClipLoader color="#3B82F6" size={30} />
                  </div>
                </div>
                <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
                  Verifying Your Email...
                </h2>
                <p className="mb-6 text-gray-600 dark:text-gray-300">
                  Please wait while we verify your email address.
                </p>
              </div>
            )}

            {verificationStatus === "verified" && (
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  <div className="flex justify-center items-center w-16 h-16 bg-green-100 rounded-full">
                    <svg
                      className="w-8 h-8 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                </div>
                <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
                  Email Verified!
                </h2>
                <p className="mb-6 text-gray-600 dark:text-gray-300">
                  Your email has been successfully verified. You'll be
                  redirected to your dashboard.
                </p>
                <div className="flex justify-center">
                  <ClipLoader color="#3B82F6" size={30} />
                </div>
              </div>
            )}

            {verificationStatus === "unverified" && (
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  <div className="flex justify-center items-center w-16 h-16 bg-blue-100 rounded-full">
                    <svg
                      className="w-8 h-8 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                </div>
                <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
                  Verify Your Email
                </h2>
                <p className="mb-6 text-gray-600 dark:text-gray-300">
                  We've sent a verification link to <strong>{userEmail}</strong>
                  . Please check your email and click the link to verify your
                  account.
                </p>

                <div className="mb-6">
                  <button
                    onClick={handleResendVerification}
                    disabled={resendLoading}
                    className="p-4 w-full text-white rounded-lg border transition cursor-pointer border-primary bg-primary hover:bg-opacity-90 disabled:opacity-50"
                  >
                    {resendLoading ? "Sending..." : "Resend Verification Email"}
                  </button>
                  {resendLoading && (
                    <div className="flex justify-center mt-2">
                      <ClipLoader color="#fff" size={20} />
                    </div>
                  )}
                </div>

                <div className="text-center">
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Didn't receive the email? Check your spam folder or{" "}
                    <button
                      onClick={handleResendVerification}
                      className="text-primary hover:underline"
                      disabled={resendLoading}
                    >
                      resend verification email
                    </button>
                  </p>
                </div>

                <div className="mt-6 text-center">
                  <p>
                    <Link to="/member/login" className="text-primary">
                      Back to Login
                    </Link>
                  </p>
                </div>
              </div>
            )}

            {verificationStatus === "error" && (
              <div className="text-center">
                <div className="flex justify-center mb-6">
                  <div className="flex justify-center items-center w-16 h-16 bg-red-100 rounded-full">
                    <svg
                      className="w-8 h-8 text-red-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                </div>
                <h2 className="mb-4 text-2xl font-bold text-black dark:text-white">
                  Verification Error
                </h2>
                <p className="mb-6 text-gray-600 dark:text-gray-300">
                  There was an error verifying your email. Please try again or
                  contact support.
                </p>

                <div className="mb-6">
                  <button
                    onClick={checkVerificationStatus}
                    className="p-4 w-full text-white rounded-lg border transition cursor-pointer border-primary bg-primary hover:bg-opacity-90"
                  >
                    Try Again
                  </button>
                </div>

                <div className="mt-6 text-center">
                  <p>
                    <Link to="/member/register" className="text-primary">
                      Back to Registration
                    </Link>
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberEmailVerifyPage;
