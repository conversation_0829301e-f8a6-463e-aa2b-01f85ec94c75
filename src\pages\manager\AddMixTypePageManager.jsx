import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import MkdSDK from "Utils/MkdSDK";
import { useNavigate } from "react-router-dom";
import { tokenExpireError } from "Src/authContext";
import { GlobalContext, showToast } from "Src/globalContext";
import { AuthContext } from "Src/authContext";
import CustomSelect2 from "Components/CustomSelect2";
import { addMixTypeAPI } from "Src/services/mixTypeServices";

import { getAllMembersForManager } from "Src/services/managerServices";

const schema = yup
  .object({
    name: yup.string().required("Name is required"),
    color: yup.string().required("Color is required"),
    price: yup
      .number()
      .required("Price is required")
      .min(0, "Price must be positive"),
  })
  .required();

const AddMixTypePageManager = () => {
  const { dispatch } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState("");
  const [producers, setProducers] = useState([]);
  const [subProjects, setSubProjects] = useState([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm({
    resolver: yupResolver(schema),
  });

  useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "mix-types",
      },
    });
    getAllProducers();
  }, []);

  const getAllProducers = async () => {
    try {
      const result = await getAllMembersForManager(
        localStorage.getItem("user")
      );
      if (!result.error) {
        const producerList = result.list.map((producer) => ({
          value: producer.id,
          label: `${producer.first_name} ${producer.last_name}`,
        }));
        setProducers(producerList);
      }
    } catch (error) {
      console.error("Error fetching producers:", error);
      tokenExpireError(dispatch, error.message);
    }
  };

  const addSubProject = () => {
    setSubProjects([
      ...subProjects,
      {
        id: Date.now(),
        type: "voiceover",
        quantity: 1,
        price: 0,
      },
    ]);
  };

  const removeSubProject = (id) => {
    setSubProjects(subProjects.filter((item) => item.id !== id));
  };

  const updateSubProject = (id, field, value) => {
    setSubProjects(
      subProjects.map((item) =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  const onSubmit = async (_data) => {
    if (!selectedMemberId) {
      showToast(globalDispatch, "Please select a producer", 4000, "error");
      return;
    }

    try {
      setIsLoading(true);

      // Validate sub-projects
      let voiceOvers = subProjects.filter((row) => row.type === "voiceover");
      let songs = subProjects.filter((row) => row.type === "song");
      let trackings = subProjects.filter((row) => row.type === "tracking");

      if (voiceOvers.length > 0) {
        let voiceOverQuantity = voiceOvers.reduce(
          (acc, row) => acc + row.quantity,
          0
        );
        if (voiceOverQuantity === 0) {
          showToast(
            globalDispatch,
            "Voiceover quantity cannot be zero",
            4000,
            "error"
          );
          setIsLoading(false);
          return;
        }
      }

      if (songs.length > 0) {
        let songQuantity = songs.reduce((acc, row) => acc + row.quantity, 0);
        if (songQuantity === 0) {
          showToast(
            globalDispatch,
            "Song quantity cannot be zero",
            4000,
            "error"
          );
          setIsLoading(false);
          return;
        }
      }

      if (trackings.length > 0) {
        let trackingQuantity = trackings.reduce(
          (acc, row) => acc + row.quantity,
          0
        );
        if (trackingQuantity === 0) {
          showToast(
            globalDispatch,
            "Tracking quantity cannot be zero",
            4000,
            "error"
          );
          setIsLoading(false);
          return;
        }
      }

      const localSubProjects = subProjects.map((item) => ({
        type: item.type,
        quantity: Number(item.quantity),
        price: Number(item.price),
      }));

      const payload = {
        name: _data.name,
        color: _data.color,
        price: Number(_data.price),
        sub_projects: JSON.stringify(localSubProjects),
        user_id: Number(selectedMemberId), // Add user_id for the selected producer
      };

      const result = await addMixTypeAPI(payload, "POST");
      if (!result.error) {
        setIsLoading(false);
        showToast(globalDispatch, "Mix Type added successfully");
        navigate(`/${authState.role}/mix-types`);
      } else {
        setIsLoading(false);
        showToast(globalDispatch, result.message, 4000, "error");
      }
    } catch (error) {
      setIsLoading(false);
      console.error("Error adding mix type:", error);
      showToast(globalDispatch, "Failed to add mix type", 4000, "error");
      tokenExpireError(dispatch, error.message);
    }
  };

  return (
    <div className="rounded-sm border shadow-default border-strokedark bg-boxdark">
      <div className="px-6.5 border-b border-stroke border-strokedark py-4">
        <h3 className="font-medium text-white">Add Mix Type for Producer</h3>
      </div>

      <form className="p-4 md:p-6 2xl:p-10" onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <label className="mb-2.5 block font-medium text-white">
              Producer
            </label>
            <CustomSelect2
              label="Producer"
              className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
              value={selectedMemberId}
              onChange={(value) => {
                setSelectedMemberId(value);
              }}
            >
              <option value="">--Select Producer--</option>
              {producers.map((producer) => (
                <option key={producer.value} value={producer.value}>
                  {producer.label}
                </option>
              ))}
            </CustomSelect2>
          </div>

          <div>
            <label className="mb-2.5 block font-medium text-white">
              Mix Type Name
            </label>
            <input
              type="text"
              placeholder="Enter mix type name"
              className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
              {...register("name")}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="mb-2.5 block font-medium text-white">Color</label>
            <input
              type="color"
              className="h-12 w-full rounded border-[1.5px] border-form-strokedark bg-form-input outline-none transition focus:border-primary active:border-primary"
              {...register("color")}
            />
            {errors.color && (
              <p className="mt-1 text-sm text-red-500">
                {errors.color.message}
              </p>
            )}
          </div>

          <div>
            <label className="mb-2.5 block font-medium text-white">Price</label>
            <input
              type="number"
              step="0.01"
              placeholder="Enter price"
              className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-5 py-3 outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary"
              {...register("price")}
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-500">
                {errors.price.message}
              </p>
            )}
          </div>
        </div>

        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-medium text-white">Sub Projects</h4>
            <button
              type="button"
              onClick={addSubProject}
              className="px-4 py-2 text-white rounded bg-primary hover:bg-opacity-90"
            >
              Add Sub Project
            </button>
          </div>

          {subProjects.map((subProject) => (
            <div
              key={subProject.id}
              className="grid grid-cols-1 gap-4 p-4 mb-4 rounded border border-stroke md:grid-cols-4 dark:border-strokedark"
            >
              <div>
                <label className="block mb-2 text-sm font-medium text-white">
                  Type
                </label>
                <select
                  value={subProject.type}
                  onChange={(e) =>
                    updateSubProject(subProject.id, "type", e.target.value)
                  }
                  className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-3 py-2 outline-none transition focus:border-primary active:border-primary"
                >
                  <option value="voiceover">Voiceover</option>
                  <option value="song">Song</option>
                  <option value="tracking">Tracking</option>
                </select>
              </div>

              <div>
                <label className="block mb-2 text-sm font-medium text-white">
                  Quantity
                </label>
                <input
                  type="number"
                  min="0"
                  value={subProject.quantity}
                  onChange={(e) =>
                    updateSubProject(
                      subProject.id,
                      "quantity",
                      parseInt(e.target.value) || 0
                    )
                  }
                  className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-3 py-2 outline-none transition focus:border-primary active:border-primary"
                />
              </div>

              <div>
                <label className="block mb-2 text-sm font-medium text-white">
                  Price
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={subProject.price}
                  onChange={(e) =>
                    updateSubProject(
                      subProject.id,
                      "price",
                      parseFloat(e.target.value) || 0
                    )
                  }
                  className="w-full rounded border-[1.5px] border-form-strokedark bg-form-input px-3 py-2 outline-none transition focus:border-primary active:border-primary"
                />
              </div>

              <div className="flex items-end">
                <button
                  type="button"
                  onClick={() => removeSubProject(subProject.id)}
                  className="px-3 py-2 w-full text-white rounded bg-danger hover:bg-opacity-90"
                >
                  Remove
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="flex gap-4 mt-6">
          <button
            type="submit"
            disabled={isLoading}
            className="flex justify-center px-6 py-2 font-medium rounded bg-primary text-gray hover:bg-opacity-90 disabled:opacity-50"
          >
            {isLoading ? "Adding..." : "Add Mix Type"}
          </button>
          <button
            type="button"
            onClick={() => navigate(`/${authState.role}/mix-types`)}
            className="flex justify-center px-6 py-2 font-medium text-white rounded border border-stroke hover:bg-meta-4"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddMixTypePageManager;
